package com.edu;

import com.edu.model.Train;
import com.edu.thread.BookingThread;

public class Main {
    public static void main(String[] args) {
        Train train = new Train(4); // 5 available seats

        BookingThread t1 = new BookingThread(train, "User-1", 2);
        BookingThread t2 = new BookingThread(train, "User-2", 1);
        BookingThread t3 = new BookingThread(train, "User-3", 1);

        System.out.println(train.getAvailableSeats());

        t1.start();
        t2.start();
        t3.start();
    }
}